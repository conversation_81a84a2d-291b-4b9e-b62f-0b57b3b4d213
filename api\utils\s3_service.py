"""
S3 service for uploading and managing audio files in AWS S3.
Handles audio file uploads and generates presigned URLs for secure access.
"""

import os
import boto3
from datetime import datetime
from typing import Optional, Tuple
from botocore.exceptions import ClientError
from api.common.api_logger import api_logger as logger
from dotenv import load_dotenv

load_dotenv()

class S3Service:
    """Service class for handling S3 operations for audio files."""

    def __init__(self):
        """Initialize the S3 service with AWS credentials and IAM role."""
        self.aws_access_key_id = os.getenv("AWS_ACCESS_KEY_ID")
        self.aws_secret_access_key = os.getenv("AWS_SECRET_ACCESS_KEY")
        self.aws_region = os.getenv("AWS_REGION", "us-east-1")
        self.bucket_name = os.getenv("S3_BUCKET_NAME")
        self.iam_role_arn = os.getenv("AWS_ROLE_ARN", "arn:aws:iam::878384876753:role/audio-url-signed-iam-role")

        if not all([self.aws_access_key_id, self.aws_secret_access_key, self.bucket_name]):
            raise ValueError("AWS credentials and bucket name must be configured in environment variables")

        # Initialize STS client to assume role
        sts_client = boto3.client(
            'sts',
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
            region_name=self.aws_region
        )

        try:
            # Assume the IAM role
            assumed_role = sts_client.assume_role(
                RoleArn=self.iam_role_arn,
                RoleSessionName='audio-url-signed-session'
            )

            # Extract temporary credentials
            credentials = assumed_role['Credentials']

            # Initialize S3 client with assumed role credentials
            self.s3_client = boto3.client(
                's3',
                aws_access_key_id=credentials['AccessKeyId'],
                aws_secret_access_key=credentials['SecretAccessKey'],
                aws_session_token=credentials['SessionToken'],
                region_name=self.aws_region,
                config=boto3.session.Config(
                    signature_version='s3v4',
                    region_name=self.aws_region
                )
            )

            logger.info(f"S3 service initialized with IAM role: {self.iam_role_arn}")

        except Exception as e:
            logger.warning(f"Failed to assume IAM role, falling back to direct credentials: {str(e)}")
            # Fallback to direct credentials if role assumption fails
            self.s3_client = boto3.client(
                's3',
                aws_access_key_id=self.aws_access_key_id,
                aws_secret_access_key=self.aws_secret_access_key,
                region_name=self.aws_region,
                config=boto3.session.Config(
                    signature_version='s3v4',
                    region_name=self.aws_region
                )
            )

        logger.info(f"S3 service initialized for bucket: {self.bucket_name}")

    def upload_audio_file(
        self,
        file_data: bytes,
        job_id: str,
        question_index: int,
        file_extension: str = "mp3"
    ) -> Tuple[bool, str]:
        """
        Upload audio file to S3 and return a presigned URL for access.

        Args:
            file_data: Audio file data as bytes
            job_id: Unique job identifier
            question_index: Index of the question
            file_extension: File extension (default: mp3)

        Returns:
            Tuple of (success, presigned_url_or_error_message)
        """
        try:
            # Generate unique filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"audio/{job_id}/question_{question_index}_{timestamp}.{file_extension}"

            # Upload to S3 without ACL (bucket doesn't support ACLs)
            self.s3_client.put_object(
                Bucket=self.bucket_name,
                Key=filename,
                Body=file_data,
                ContentType="audio/mpeg",
                CacheControl="max-age=31536000",  # Cache for 1 year
                Metadata={
                    'job_id': job_id,
                    'question_index': str(question_index),
                    'upload_time': timestamp
                }
            )

            # Generate presigned URL for secure access
            s3_url = self.generate_presigned_url(filename, expiration=3600)  # 1 hour expiration
            if not s3_url:
                return False, "Failed to generate presigned URL"

            return True, s3_url

        except Exception as e:
            error_msg = f"S3 upload error: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def generate_presigned_url(self, s3_key: str, expiration: int = 3600) -> Optional[str]:
        """
        Generate a short-lived S3 presigned URL for direct file access.
        Uses proper presigned URL generation with ExpiresIn parameter.

        Args:
            s3_key: S3 object key
            expiration: URL expiration time in seconds (default: 1 hour = 3600)

        Returns:
            Short-lived presigned URL or None if error
        """
        try:
            # Generate presigned URL for download with explicit configuration
            presigned_url = self.s3_client.generate_presigned_url(
                ClientMethod='get_object',
                Params={
                    'Bucket': self.bucket_name,
                    'Key': s3_key
                },
                ExpiresIn=expiration,  # Expiration in seconds from now
                HttpMethod='GET'
            )
            logger.info(f"Generated presigned URL with {expiration}s expiration")
            return presigned_url
        except ClientError as e:
            logger.error(f"Error generating presigned URL: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error generating presigned URL: {str(e)}")
            return None

# Global S3 service instance
s3_service = S3Service()
