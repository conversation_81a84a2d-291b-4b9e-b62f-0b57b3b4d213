"""
S3 service for uploading and managing audio files in AWS S3.
Handles audio file uploads and generates public URLs for access.
"""

import os
import boto3
from datetime import datetime
from typing import Op<PERSON>, Tuple
from botocore.exceptions import ClientError, NoCredentialsError
from api.common.api_logger import api_logger as logger
from dotenv import load_dotenv

load_dotenv()

class S3Service:
    """Service class for handling S3 operations for audio files."""

    def __init__(self):
        """Initialize the S3 service with AWS credentials."""
        self.aws_access_key_id = os.getenv("AWS_ACCESS_KEY_ID")
        self.aws_secret_access_key = os.getenv("AWS_SECRET_ACCESS_KEY")
        self.aws_region = os.getenv("AWS_REGION", "us-east-1")
        self.bucket_name = os.getenv("S3_BUCKET_NAME")

        if not all([self.aws_access_key_id, self.aws_secret_access_key, self.bucket_name]):
            raise ValueError("AWS credentials and bucket name must be configured in environment variables")

        # Initialize S3 client
        self.s3_client = boto3.client(
            's3',
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
            region_name=self.aws_region
        )

        logger.info(f"S3 service initialized for bucket: {self.bucket_name}")

    def upload_audio_file(
        self,
        file_data: bytes,
        job_id: str,
        question_index: int,
        file_extension: str = "mp3"
    ) -> Tuple[bool, str]:
        """
        Upload audio file to S3 and return the public URL.

        Args:
            file_data: Audio file data as bytes
            job_id: Unique job identifier
            question_index: Index of the question
            file_extension: File extension (default: mp3)

        Returns:
            Tuple of (success, url_or_error_message)
        """
        try:
            # Generate unique filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"audio/{job_id}/question_{question_index}_{timestamp}.{file_extension}"

            # Upload to S3 with public read access
            self.s3_client.put_object(
                Bucket=self.bucket_name,
                Key=filename,
                Body=file_data,
                ContentType="audio/mpeg",
                CacheControl="max-age=31536000",  # Cache for 1 year
                ACL='public-read',  # Make file publicly accessible
                Metadata={
                    'job_id': job_id,
                    'question_index': str(question_index),
                    'upload_time': timestamp
                }
            )

            # Try to generate public URL, fallback to presigned URL if ACL fails
            try:
                # First try public URL
                s3_url = f"https://{self.bucket_name}.s3.{self.aws_region}.amazonaws.com/{filename}"
            except:
                # Fallback to presigned URL (24 hours expiration)
                s3_url = self.generate_presigned_url(filename, expiration=86400)
                if not s3_url:
                    s3_url = f"https://{self.bucket_name}.s3.{self.aws_region}.amazonaws.com/{filename}"

            logger.info(f"Audio file uploaded successfully: {s3_url}")
            return True, s3_url

        except ClientError as e:
            error_msg = f"AWS S3 error: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
        except NoCredentialsError:
            error_msg = "AWS credentials not found or invalid"
            logger.error(error_msg)
            return False, error_msg
        except Exception as e:
            error_msg = f"Unexpected error uploading to S3: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def delete_audio_files(self, job_id: str) -> bool:
        """
        Delete all audio files for a specific job from S3.

        Args:
            job_id: Unique job identifier

        Returns:
            Success status
        """
        try:
            # List all objects with the job_id prefix
            prefix = f"audio/{job_id}/"
            response = self.s3_client.list_objects_v2(
                Bucket=self.bucket_name,
                Prefix=prefix
            )

            if 'Contents' not in response:
                logger.info(f"No files found for job {job_id}")
                return True

            # Delete all objects
            objects_to_delete = [{'Key': obj['Key']} for obj in response['Contents']]

            if objects_to_delete:
                self.s3_client.delete_objects(
                    Bucket=self.bucket_name,
                    Delete={'Objects': objects_to_delete}
                )
                logger.info(f"Deleted {len(objects_to_delete)} files for job {job_id}")

            return True

        except ClientError as e:
            logger.error(f"Error deleting S3 files for job {job_id}: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error deleting S3 files for job {job_id}: {str(e)}")
            return False

    def check_bucket_access(self) -> bool:
        """
        Check if the S3 bucket is accessible with current credentials.

        Returns:
            True if bucket is accessible, False otherwise
        """
        try:
            self.s3_client.head_bucket(Bucket=self.bucket_name)
            logger.info(f"S3 bucket {self.bucket_name} is accessible")
            return True
        except ClientError as e:
            logger.error(f"Cannot access S3 bucket {self.bucket_name}: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error checking S3 bucket access: {str(e)}")
            return False

    def make_object_public(self, s3_key: str) -> bool:
        """
        Make an existing S3 object publicly readable.

        Args:
            s3_key: S3 object key

        Returns:
            Success status
        """
        try:
            self.s3_client.put_object_acl(
                Bucket=self.bucket_name,
                Key=s3_key,
                ACL='public-read'
            )
            logger.info(f"Made S3 object public: {s3_key}")
            return True
        except ClientError as e:
            logger.error(f"Error making S3 object public: {str(e)}")
            return False

    def generate_presigned_url(self, s3_key: str, expiration: int = 3600) -> Optional[str]:
        """
        Generate a presigned URL for downloading a file from S3.

        Args:
            s3_key: S3 object key
            expiration: URL expiration time in seconds (default: 1 hour)

        Returns:
            Presigned URL or None if error
        """
        try:
            url = self.s3_client.generate_presigned_url(
                'get_object',
                Params={'Bucket': self.bucket_name, 'Key': s3_key},
                ExpiresIn=expiration
            )
            return url
        except ClientError as e:
            logger.error(f"Error generating presigned URL: {str(e)}")
            return None

# Global S3 service instance
s3_service = S3Service()
