"""
Database operations for audio generation jobs.
Handles job tracking, status updates, and file metadata storage.
"""

import json
from datetime import datetime
from typing import List, Optional, Tuple
from api.common.api_logger import api_logger as logger
from api.common.pydantic_models import AudioFile, AudioJobStatus

# In-memory storage for audio jobs (for MVP - can be replaced with database later)
audio_jobs_storage = {}

class AudioJobsDB:
    """Database operations for audio generation jobs."""

    @staticmethod
    def create_job(
        job_id: str,
        questions: List[str],
        voice_id: str,
        model: str = "eleven_flash_v2_5"
    ) -> bool:
        """
        Create a new audio generation job.

        Args:
            job_id: Unique job identifier
            questions: List of questions to process
            voice_id: ElevenLabs voice ID
            model: TTS model to use

        Returns:
            Success status
        """
        try:
            job_data = {
                "job_id": job_id,
                "status": "pending",
                "progress": 0,
                "questions": questions,
                "voice_id": voice_id,
                "model": model,
                "total_questions": len(questions),
                "audio_files": [],
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
                "error_message": None
            }

            audio_jobs_storage[job_id] = job_data
            logger.info(f"Created audio job: {job_id}")
            return True

        except Exception as e:
            logger.error(f"Error creating audio job {job_id}: {str(e)}")
            return False

    @staticmethod
    def update_job_status(
        job_id: str,
        status: str,
        progress: int = None,
        error_message: str = None
    ) -> bool:
        """
        Update job status and progress.

        Args:
            job_id: Job identifier
            status: New status (pending, processing, completed, failed)
            progress: Progress percentage (0-100)
            error_message: Error message if status is failed

        Returns:
            Success status
        """
        try:
            if job_id not in audio_jobs_storage:
                logger.error(f"Job not found: {job_id}")
                return False

            job_data = audio_jobs_storage[job_id]
            job_data["status"] = status
            job_data["updated_at"] = datetime.now().isoformat()

            if progress is not None:
                job_data["progress"] = progress

            if error_message:
                job_data["error_message"] = error_message

            logger.info(f"Updated job {job_id}: status={status}, progress={progress}")
            return True

        except Exception as e:
            logger.error(f"Error updating job {job_id}: {str(e)}")
            return False

    @staticmethod
    def add_audio_file(job_id: str, audio_file: AudioFile) -> bool:
        """
        Add a completed audio file to the job.

        Args:
            job_id: Job identifier
            audio_file: AudioFile object with file details

        Returns:
            Success status
        """
        try:
            if job_id not in audio_jobs_storage:
                logger.error(f"Job not found: {job_id}")
                return False

            job_data = audio_jobs_storage[job_id]

            # Convert AudioFile to dict for storage
            audio_file_dict = {
                "question_index": audio_file.question_index,
                "question_text": audio_file.question_text,
                "file_url": audio_file.file_url,
                "duration_seconds": audio_file.duration_seconds
            }

            job_data["audio_files"].append(audio_file_dict)
            job_data["updated_at"] = datetime.now().isoformat()

            # Update progress
            completed_files = len(job_data["audio_files"])
            total_files = job_data["total_questions"]
            progress = int((completed_files / total_files) * 100)
            job_data["progress"] = progress

            logger.info(f"Added audio file to job {job_id}: {audio_file.file_url}")
            return True

        except Exception as e:
            logger.error(f"Error adding audio file to job {job_id}: {str(e)}")
            return False

    @staticmethod
    def get_job_status(job_id: str) -> Optional[AudioJobStatus]:
        """
        Get current status of an audio generation job.

        Args:
            job_id: Job identifier

        Returns:
            AudioJobStatus object or None if not found
        """
        try:
            if job_id not in audio_jobs_storage:
                return None

            job_data = audio_jobs_storage[job_id]

            # Convert stored audio files back to AudioFile objects
            audio_files = []
            for file_data in job_data["audio_files"]:
                audio_file = AudioFile(
                    question_index=file_data["question_index"],
                    question_text=file_data["question_text"],
                    file_url=file_data["file_url"],
                    duration_seconds=file_data["duration_seconds"]
                )
                audio_files.append(audio_file)

            return AudioJobStatus(
                job_id=job_id,
                status=job_data["status"],
                progress=job_data["progress"],
                audio_files=audio_files,
                error_message=job_data.get("error_message")
            )

        except Exception as e:
            logger.error(f"Error getting job status {job_id}: {str(e)}")
            return None

    @staticmethod
    def get_job_data(job_id: str) -> Optional[dict]:
        """
        Get complete job data.

        Args:
            job_id: Job identifier

        Returns:
            Job data dictionary or None if not found
        """
        return audio_jobs_storage.get(job_id)

    @staticmethod
    def delete_job(job_id: str) -> bool:
        """
        Delete a job from storage.

        Args:
            job_id: Job identifier

        Returns:
            Success status
        """
        try:
            if job_id in audio_jobs_storage:
                del audio_jobs_storage[job_id]
                logger.info(f"Deleted job: {job_id}")
                return True
            return False

        except Exception as e:
            logger.error(f"Error deleting job {job_id}: {str(e)}")
            return False

    @staticmethod
    def list_jobs() -> List[dict]:
        """
        List all jobs in storage.

        Returns:
            List of job data dictionaries
        """
        return list(audio_jobs_storage.values())

    @staticmethod
    def cleanup_old_jobs(hours: int = 24) -> int:
        """
        Clean up jobs older than specified hours.

        Args:
            hours: Age threshold in hours

        Returns:
            Number of jobs cleaned up
        """
        try:
            current_time = datetime.now()
            jobs_to_delete = []

            for job_id, job_data in audio_jobs_storage.items():
                created_at = datetime.fromisoformat(job_data["created_at"])
                age_hours = (current_time - created_at).total_seconds() / 3600

                if age_hours > hours:
                    jobs_to_delete.append(job_id)

            for job_id in jobs_to_delete:
                del audio_jobs_storage[job_id]

            logger.info(f"Cleaned up {len(jobs_to_delete)} old jobs")
            return len(jobs_to_delete)

        except Exception as e:
            logger.error(f"Error cleaning up old jobs: {str(e)}")
            return 0

# Global instance
audio_jobs_db = AudioJobsDB()
