"""
Database operations for audio generation jobs.
Handles job tracking, status updates, and file metadata storage.
"""

import json
from datetime import datetime
from typing import List, Optional, Tuple
from api.common.api_logger import api_logger as logger
from api.common.pydantic_models import AudioFile, AudioJobStatus
from api.database.connection_db import get_connection

class AudioJobsDB:
    """Database operations for audio generation jobs."""

    @staticmethod
    def _ensure_table_exists():
        """Create the audio_jobs table if it doesn't exist."""
        connection = None
        try:
            connection = get_connection(is_ca=False)
            with connection.cursor() as cursor:
                # Create audio_jobs table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS audio_jobs (
                        job_id VARCHAR(255) PRIMARY KEY,
                        status VARCHAR(50) NOT NULL,
                        progress INT DEFAULT 0,
                        questions JSON NOT NULL,
                        voice_id VARCHAR(255) NOT NULL,
                        model VARCHAR(100) NOT NULL,
                        total_questions INT NOT NULL,
                        audio_files JSON DEFAULT '[]',
                        created_at DATETIME NOT NULL,
                        updated_at DATETIME NOT NULL,
                        error_message TEXT NULL
                    )
                """)
                connection.commit()
                logger.info("Audio jobs table ensured to exist")
        except Exception as e:
            logger.error(f"Error creating audio_jobs table: {str(e)}")
        finally:
            if connection:
                connection.close()

    @staticmethod
    def create_job(
        job_id: str,
        questions: List[str],
        voice_id: str,
        model: str = "eleven_flash_v2_5"
    ) -> bool:
        """
        Create a new audio generation job.

        Args:
            job_id: Unique job identifier
            questions: List of questions to process
            voice_id: ElevenLabs voice ID
            model: TTS model to use

        Returns:
            Success status
        """
        AudioJobsDB._ensure_table_exists()
        connection = None
        try:
            connection = get_connection(is_ca=False)
            with connection.cursor() as cursor:
                now = datetime.now()
                cursor.execute("""
                    INSERT INTO audio_jobs
                    (job_id, status, progress, questions, voice_id, model, total_questions,
                     audio_files, created_at, updated_at, error_message)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    job_id, "pending", 0, json.dumps(questions), voice_id, model,
                    len(questions), json.dumps([]), now, now, None
                ))
                connection.commit()
                logger.info(f"Created audio job: {job_id}")
                return True

        except Exception as e:
            logger.error(f"Error creating audio job {job_id}: {str(e)}")
            return False
        finally:
            if connection:
                connection.close()

    @staticmethod
    def update_job_status(
        job_id: str,
        status: str,
        progress: int = None,
        error_message: str = None
    ) -> bool:
        """
        Update job status and progress.

        Args:
            job_id: Job identifier
            status: New status (pending, processing, completed, failed)
            progress: Progress percentage (0-100)
            error_message: Error message if status is failed

        Returns:
            Success status
        """
        connection = None
        try:
            connection = get_connection(is_ca=False)
            with connection.cursor() as cursor:
                # Build dynamic update query
                update_fields = ["status = %s", "updated_at = %s"]
                params = [status, datetime.now()]

                if progress is not None:
                    update_fields.append("progress = %s")
                    params.append(progress)

                if error_message:
                    update_fields.append("error_message = %s")
                    params.append(error_message)

                params.append(job_id)  # For WHERE clause

                query = f"UPDATE audio_jobs SET {', '.join(update_fields)} WHERE job_id = %s"
                cursor.execute(query, params)
                connection.commit()

                if cursor.rowcount == 0:
                    logger.error(f"Job not found: {job_id}")
                    return False

                logger.info(f"Updated job {job_id}: status={status}, progress={progress}")
                return True

        except Exception as e:
            logger.error(f"Error updating job {job_id}: {str(e)}")
            return False
        finally:
            if connection:
                connection.close()

    @staticmethod
    def add_audio_file(job_id: str, audio_file: AudioFile) -> bool:
        """
        Add a completed audio file to the job.

        Args:
            job_id: Job identifier
            audio_file: AudioFile object with file details

        Returns:
            Success status
        """
        connection = None
        try:
            connection = get_connection(is_ca=False)
            with connection.cursor() as cursor:
                # First, get current audio files and total questions
                cursor.execute("SELECT audio_files, total_questions FROM audio_jobs WHERE job_id = %s", (job_id,))
                result = cursor.fetchone()

                if not result:
                    logger.error(f"Job not found: {job_id}")
                    return False

                # Parse current audio files
                current_files = json.loads(result['audio_files'])
                total_questions = result['total_questions']

                # Convert AudioFile to dict for storage
                audio_file_dict = {
                    "question_index": audio_file.question_index,
                    "question_text": audio_file.question_text,
                    "file_url": audio_file.file_url,
                    "duration_seconds": audio_file.duration_seconds
                }

                # Add new file
                current_files.append(audio_file_dict)

                # Calculate progress
                progress = int((len(current_files) / total_questions) * 100)

                # Update database
                cursor.execute("""
                    UPDATE audio_jobs
                    SET audio_files = %s, progress = %s, updated_at = %s
                    WHERE job_id = %s
                """, (json.dumps(current_files), progress, datetime.now(), job_id))
                connection.commit()

                logger.info(f"Added audio file to job {job_id}: {audio_file.file_url}")
                return True

        except Exception as e:
            logger.error(f"Error adding audio file to job {job_id}: {str(e)}")
            return False
        finally:
            if connection:
                connection.close()

    @staticmethod
    def get_job_status(job_id: str) -> Optional[AudioJobStatus]:
        """
        Get current status of an audio generation job.

        Args:
            job_id: Job identifier

        Returns:
            AudioJobStatus object or None if not found
        """
        connection = None
        try:
            connection = get_connection(is_ca=False)
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT job_id, status, progress, audio_files, error_message
                    FROM audio_jobs WHERE job_id = %s
                """, (job_id,))
                result = cursor.fetchone()

                if not result:
                    return None

                # Convert stored audio files back to AudioFile objects
                audio_files = []
                files_data = json.loads(result['audio_files'])
                for file_data in files_data:
                    audio_file = AudioFile(
                        question_index=file_data["question_index"],
                        question_text=file_data["question_text"],
                        file_url=file_data["file_url"],
                        duration_seconds=file_data["duration_seconds"]
                    )
                    audio_files.append(audio_file)

                return AudioJobStatus(
                    job_id=job_id,
                    status=result["status"],
                    progress=result["progress"],
                    audio_files=audio_files,
                    error_message=result.get("error_message")
                )

        except Exception as e:
            logger.error(f"Error getting job status {job_id}: {str(e)}")
            return None
        finally:
            if connection:
                connection.close()

    @staticmethod
    def get_job_data(job_id: str) -> Optional[dict]:
        """
        Get complete job data.

        Args:
            job_id: Job identifier

        Returns:
            Job data dictionary or None if not found
        """
        connection = None
        try:
            connection = get_connection(is_ca=False)
            with connection.cursor() as cursor:
                cursor.execute("SELECT * FROM audio_jobs WHERE job_id = %s", (job_id,))
                result = cursor.fetchone()

                if not result:
                    return None

                # Convert database row to dictionary format
                job_data = {
                    "job_id": result["job_id"],
                    "status": result["status"],
                    "progress": result["progress"],
                    "questions": json.loads(result["questions"]),
                    "voice_id": result["voice_id"],
                    "model": result["model"],
                    "total_questions": result["total_questions"],
                    "audio_files": json.loads(result["audio_files"]),
                    "created_at": result["created_at"].isoformat() if result["created_at"] else None,
                    "updated_at": result["updated_at"].isoformat() if result["updated_at"] else None,
                    "error_message": result["error_message"]
                }
                return job_data

        except Exception as e:
            logger.error(f"Error getting job data {job_id}: {str(e)}")
            return None
        finally:
            if connection:
                connection.close()

    @staticmethod
    def delete_job(job_id: str) -> bool:
        """
        Delete a job from storage.

        Args:
            job_id: Job identifier

        Returns:
            Success status
        """
        connection = None
        try:
            connection = get_connection(is_ca=False)
            with connection.cursor() as cursor:
                cursor.execute("DELETE FROM audio_jobs WHERE job_id = %s", (job_id,))
                connection.commit()

                if cursor.rowcount > 0:
                    logger.info(f"Deleted job: {job_id}")
                    return True
                return False

        except Exception as e:
            logger.error(f"Error deleting job {job_id}: {str(e)}")
            return False
        finally:
            if connection:
                connection.close()

    @staticmethod
    def list_jobs() -> List[dict]:
        """
        List all jobs in storage.

        Returns:
            List of job data dictionaries
        """
        connection = None
        try:
            connection = get_connection(is_ca=False)
            with connection.cursor() as cursor:
                cursor.execute("SELECT * FROM audio_jobs ORDER BY created_at DESC")
                results = cursor.fetchall()

                jobs = []
                for result in results:
                    job_data = {
                        "job_id": result["job_id"],
                        "status": result["status"],
                        "progress": result["progress"],
                        "questions": json.loads(result["questions"]),
                        "voice_id": result["voice_id"],
                        "model": result["model"],
                        "total_questions": result["total_questions"],
                        "audio_files": json.loads(result["audio_files"]),
                        "created_at": result["created_at"].isoformat() if result["created_at"] else None,
                        "updated_at": result["updated_at"].isoformat() if result["updated_at"] else None,
                        "error_message": result["error_message"]
                    }
                    jobs.append(job_data)

                return jobs

        except Exception as e:
            logger.error(f"Error listing jobs: {str(e)}")
            return []
        finally:
            if connection:
                connection.close()

    @staticmethod
    def cleanup_old_jobs(hours: int = 24) -> int:
        """
        Clean up jobs older than specified hours.

        Args:
            hours: Age threshold in hours

        Returns:
            Number of jobs cleaned up
        """
        connection = None
        try:
            connection = get_connection(is_ca=False)
            with connection.cursor() as cursor:
                # Delete jobs older than specified hours
                cursor.execute("""
                    DELETE FROM audio_jobs
                    WHERE created_at < DATE_SUB(NOW(), INTERVAL %s HOUR)
                """, (hours,))
                connection.commit()

                deleted_count = cursor.rowcount
                logger.info(f"Cleaned up {deleted_count} old jobs")
                return deleted_count

        except Exception as e:
            logger.error(f"Error cleaning up old jobs: {str(e)}")
            return 0
        finally:
            if connection:
                connection.close()

# Global instance
audio_jobs_db = AudioJobsDB()
