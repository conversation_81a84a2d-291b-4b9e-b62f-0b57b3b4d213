2025-06-04 00:04:38,239 - api_logger - INFO - S3 service initialized for bucket: my-interview-practice
2025-06-04 00:04:39,583 - api_logger - INFO - S3 bucket my-interview-practice is accessible
2025-06-04 02:38:40,610 - api_logger - INFO - S3 service initialized for bucket: audio-url-signed
2025-06-04 02:38:40,614 - api_logger - INFO - S3 service initialized for bucket: audio-url-signed
2025-06-04 02:38:41,929 - api_logger - ERROR - Cannot access S3 bucket audio-url-signed: An error occurred (403) when calling the HeadBucket operation: Forbidden
2025-06-04 02:39:26,303 - api_logger - INFO - S3 service initialized for bucket: audio-url-signed
2025-06-04 02:39:26,308 - api_logger - INFO - S3 service initialized for bucket: audio-url-signed
2025-06-04 02:40:20,132 - api_logger - INFO - S3 service initialized for bucket: audio-url-signed
2025-06-04 02:40:20,135 - api_logger - INFO - S3 service initialized for bucket: audio-url-signed
2025-06-04 02:40:22,641 - api_logger - ERROR - Cannot access S3 bucket audio-url-signed: An error occurred (403) when calling the HeadBucket operation: Forbidden
2025-06-04 02:42:01,101 - api_logger - INFO - S3 service initialized for bucket: audio-url-signed
2025-06-04 02:42:01,105 - api_logger - INFO - S3 service initialized for bucket: audio-url-signed
2025-06-04 02:42:03,612 - api_logger - ERROR - Cannot access S3 bucket audio-url-signed: An error occurred (403) when calling the HeadBucket operation: Forbidden
2025-06-04 02:42:03,881 - api_logger - ERROR - AWS S3 error: An error occurred (AccessControlListNotSupported) when calling the PutObject operation: The bucket does not allow ACLs
2025-06-04 02:42:51,545 - api_logger - INFO - S3 service initialized for bucket: audio-url-signed
2025-06-04 02:42:51,545 - api_logger - INFO - S3 service initialized for bucket: audio-url-signed
2025-06-04 02:42:54,718 - api_logger - ERROR - Cannot access S3 bucket audio-url-signed: An error occurred (403) when calling the HeadBucket operation: Forbidden
2025-06-04 02:42:55,155 - api_logger - INFO - Bucket doesn't support ACLs, uploading without ACL
2025-06-04 02:42:56,064 - api_logger - INFO - S3 service initialized for bucket: audio-url-signed
2025-06-04 02:42:56,538 - api_logger - INFO - Audio file uploaded successfully: https://audio-url-signed.s3.amazonaws.com/audio/test_job_123/question_1_20250604_024254.mp3?AWSAccessKeyId=AKIA4ZA6RRTIZG6V56SL&Signature=tM7TYFTz3CBtQ3n018mC3gf24ZQ%3D&Expires=1749116576
2025-06-04 02:42:56,824 - api_logger - ERROR - Error deleting S3 files for job test_job_123: An error occurred (AccessDenied) when calling the ListObjectsV2 operation: User: arn:aws:iam::878384876753:user/s3-audio-url-signed-iam-user is not authorized to perform: s3:ListBucket on resource: "arn:aws:s3:::audio-url-signed" because no identity-based policy allows the s3:ListBucket action
2025-06-04 02:42:57,311 - api_logger - ERROR - Cannot access S3 bucket audio-url-signed: An error occurred (403) when calling the HeadBucket operation: Forbidden
2025-06-04 02:42:57,311 - api_logger - WARNING - S3 bucket access check failed - audio uploads may fail
2025-06-04 02:51:09,073 - api_logger - INFO - S3 service initialized for bucket: audio-url-signed
2025-06-04 02:51:09,079 - api_logger - INFO - S3 service initialized for bucket: audio-url-signed
2025-06-04 02:51:11,343 - api_logger - ERROR - Cannot access S3 bucket audio-url-signed: An error occurred (403) when calling the HeadBucket operation: Forbidden
2025-06-04 02:51:11,884 - api_logger - INFO - Audio file uploaded successfully: https://audio-url-signed.s3.amazonaws.com/audio/test_job_123/question_1_20250604_025111.mp3?AWSAccessKeyId=AKIA4ZA6RRTIZG6V56SL&Signature=QbV8jkBLE2rF2dpb1VN7BOh5b0o%3D&Expires=1749117071
2025-06-04 02:51:12,150 - api_logger - ERROR - Error deleting S3 files for job test_job_123: An error occurred (AccessDenied) when calling the ListObjectsV2 operation: User: arn:aws:iam::878384876753:user/s3-audio-url-signed-iam-user is not authorized to perform: s3:ListBucket on resource: "arn:aws:s3:::audio-url-signed" because no identity-based policy allows the s3:ListBucket action
2025-06-04 02:52:46,658 - api_logger - INFO - S3 service initialized for bucket: audio-url-signed
2025-06-04 02:52:47,850 - api_logger - ERROR - Cannot access S3 bucket audio-url-signed: An error occurred (403) when calling the HeadBucket operation: Forbidden
2025-06-04 02:52:47,852 - api_logger - WARNING - S3 bucket access check failed - audio uploads may fail
2025-06-04 12:12:43,640 - api_logger - INFO - S3 service initialized for bucket: audio-url-signed
2025-06-04 12:12:44,935 - api_logger - ERROR - Cannot access S3 bucket audio-url-signed: An error occurred (403) when calling the HeadBucket operation: Forbidden
2025-06-04 12:12:44,935 - api_logger - WARNING - S3 bucket access check failed - audio uploads may fail
2025-06-04 12:14:35,150 - api_logger - INFO - S3 service initialized for bucket: audio-url-signed
2025-06-04 12:14:35,883 - api_logger - INFO - S3 bucket access check failed (expected with limited IAM permissions): An error occurred (403) when calling the HeadBucket operation: Forbidden
2025-06-04 12:15:43,795 - api_logger - INFO - S3 service initialized for bucket: audio-url-signed
2025-06-04 12:18:21,635 - api_logger - INFO - S3 service initialized for bucket: audio-url-signed
2025-06-04 12:19:05,913 - api_logger - INFO - S3 service initialized for bucket: audio-url-signed
2025-06-04 12:20:51,723 - api_logger - INFO - S3 service initialized for bucket: audio-url-signed
2025-06-04 12:20:55,808 - api_logger - INFO - S3 service initialized for bucket: audio-url-signed
2025-06-04 12:36:22,673 - api_logger - INFO - Created audio job: audio_job_5a85e996a616
2025-06-04 12:36:22,679 - api_logger - INFO - Updated job audio_job_5a85e996a616: status=processing, progress=0
2025-06-04 12:36:23,251 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/cgSgspJ2msm6clMCkdW9 "HTTP/1.1 200 OK"
2025-06-04 12:36:24,591 - api_logger - INFO - Audio generated and uploaded successfully: https://audio-url-signed.s3.amazonaws.com/audio/audio_job_5a85e996a616/question_0_20250604_123623.mp3?AWSAccessKeyId=AKIA4ZA6RRTIZG6V56SL&Signature=LdeZHlAdQNbgVvP0GV2jTobZM4c%3D&Expires=1749152184
2025-06-04 12:36:25,036 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/cgSgspJ2msm6clMCkdW9 "HTTP/1.1 200 OK"
2025-06-04 12:36:25,596 - api_logger - INFO - Audio generated and uploaded successfully: https://audio-url-signed.s3.amazonaws.com/audio/audio_job_5a85e996a616/question_1_20250604_123625.mp3?AWSAccessKeyId=AKIA4ZA6RRTIZG6V56SL&Signature=pfOgnAxrvouzSV9Jz4AM%2Fu8YZMA%3D&Expires=1749152185
2025-06-04 12:36:25,596 - api_logger - INFO - Added audio file to job audio_job_5a85e996a616: https://audio-url-signed.s3.amazonaws.com/audio/audio_job_5a85e996a616/question_0_20250604_123623.mp3?AWSAccessKeyId=AKIA4ZA6RRTIZG6V56SL&Signature=LdeZHlAdQNbgVvP0GV2jTobZM4c%3D&Expires=1749152184
2025-06-04 12:36:25,596 - api_logger - INFO - Added audio file to job audio_job_5a85e996a616: https://audio-url-signed.s3.amazonaws.com/audio/audio_job_5a85e996a616/question_1_20250604_123625.mp3?AWSAccessKeyId=AKIA4ZA6RRTIZG6V56SL&Signature=pfOgnAxrvouzSV9Jz4AM%2Fu8YZMA%3D&Expires=1749152185
2025-06-04 12:36:25,596 - api_logger - INFO - Updated job audio_job_5a85e996a616: status=completed, progress=100
2025-06-04 12:42:50,703 - api_logger - INFO - S3 service initialized for bucket: audio-url-signed
2025-06-04 12:46:10,790 - api_logger - INFO - S3 service initialized for bucket: audio-url-signed
2025-06-04 13:09:52,210 - api_logger - INFO - S3 service initialized for bucket: audio-url-signed
2025-06-04 13:10:54,652 - api_logger - INFO - S3 service initialized for bucket: audio-url-signed
