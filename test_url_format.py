#!/usr/bin/env python3
"""
Test script to show current S3 URL format
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from api.utils.s3_service import s3_service

def test_url_format():
    """Test current URL format generation."""
    print("=== S3 URL Format Test ===")
    print(f"Bucket: {s3_service.bucket_name}")
    print(f"Region: {s3_service.aws_region}")
    print(f"Access Key: {s3_service.aws_access_key_id}")
    print()
    
    # Test presigned URL generation
    test_key = "audio/test_job_123/question_1_20250604_120000.mp3"
    presigned_url = s3_service.generate_presigned_url(test_key, 3600)
    
    print("=== Generated URL Formats ===")
    print("1. Presigned URL (what we currently generate):")
    print(f"   {presigned_url}")
    print()
    
    print("2. Direct URL (fallback format):")
    direct_url = f"https://{s3_service.bucket_name}.s3.{s3_service.aws_region}.amazonaws.com/{test_key}"
    print(f"   {direct_url}")
    print()
    
    print("3. Alternative regional format:")
    regional_url = f"https://s3.{s3_service.aws_region}.amazonaws.com/{s3_service.bucket_name}/{test_key}"
    print(f"   {regional_url}")
    print()
    
    print("=== Your S3 Console Link Format ===")
    console_url = f"https://{s3_service.aws_region}.console.aws.amazon.com/s3/buckets/{s3_service.bucket_name}"
    print(f"Console: {console_url}")
    print()
    
    print("Which format do you want for the audio file URLs?")

if __name__ == "__main__":
    test_url_format()
