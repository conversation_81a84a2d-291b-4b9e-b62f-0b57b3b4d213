#!/usr/bin/env python3
"""
Test script to verify S3 connection with new credentials and bucket.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from api.utils.s3_service import S3Service
from api.common.api_logger import api_logger as logger

def test_s3_connection():
    """Test S3 service connection and basic operations."""
    print("Testing S3 connection with new credentials...")

    try:
        # Initialize S3 service
        s3_service = S3Service()
        print(f"✓ S3 service initialized successfully")
        print(f"  - Bucket: {s3_service.bucket_name}")
        print(f"  - Region: {s3_service.aws_region}")

        # Test basic AWS connection first
        print("\nTesting AWS connection...")
        try:
            # Try to get bucket location to test basic AWS access
            response = s3_service.s3_client.get_bucket_location(Bucket=s3_service.bucket_name)
            print("✓ AWS connection successful")
            print(f"  - Bucket location: {response.get('LocationConstraint', 'us-east-1')}")
        except Exception as e:
            print(f"Note: Could not get bucket location: {str(e)}")
            print("This might be normal if bucket doesn't exist yet or user has limited permissions")

        # Test bucket access
        print(f"\nTesting bucket access for '{s3_service.bucket_name}'...")
        bucket_accessible = s3_service.check_bucket_access()
        if bucket_accessible:
            print("✓ Bucket access successful")
        else:
            print("✗ Bucket access failed (HeadBucket operation)")
            print("This might be normal - trying direct upload test instead...")
            
        # Test upload with a small test file
        print("\nTesting file upload...")
        test_data = b"This is a test audio file content"
        success, result = s3_service.upload_audio_file(
            file_data=test_data,
            job_id="test_job_123",
            question_index=1,
            file_extension="mp3"
        )
        
        if success:
            print(f"✓ Test file uploaded successfully")
            print(f"  - URL: {result}")
            
            # Test file deletion
            print("\nTesting file deletion...")
            if s3_service.delete_audio_files("test_job_123"):
                print("✓ Test file deleted successfully")
            else:
                print("✗ Test file deletion failed")
                
        else:
            print(f"✗ Test file upload failed: {result}")
            return False
            
        print("\n🎉 All S3 tests passed successfully!")
        return True
        
    except Exception as e:
        print(f"✗ S3 test failed with error: {str(e)}")
        logger.error(f"S3 test error: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_s3_connection()
    sys.exit(0 if success else 1)
